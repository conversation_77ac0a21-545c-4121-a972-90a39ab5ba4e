# 连连看游戏

一个使用HTML、CSS和JavaScript开发的经典连连看游戏，使用imgs文件夹中的图片作为游戏素材。

## 游戏特色

- 🎮 经典连连看玩法
- 🖼️ 使用本地图片资源
- ⏱️ 实时计时功能
- 🎯 分数统计系统
- 🔄 重新排列功能
- 💫 流畅的动画效果
- 📱 响应式设计

## 游戏规则

1. **目标**：消除所有配对的图片卡片
2. **操作**：点击两张相同的图片进行匹配
3. **连接规则**：两张卡片之间可以通过以下方式连接：
   - 直线连接（水平或垂直）
   - 一个转角连接
   - 两个转角连接（通过边界）
4. **胜利条件**：消除所有卡片对

## 游戏功能

### 主要功能
- **新游戏**：开始一局新的游戏
- **重新排列**：重新打乱当前游戏的卡片位置
- **计时器**：显示游戏进行时间
- **分数系统**：每成功匹配一对获得100分
- **剩余统计**：显示还需要匹配的卡片对数

### 视觉效果
- 卡片悬停放大效果
- 选中卡片高亮显示
- 匹配成功的连接线动画
- 已匹配卡片淡化效果
- 渐变背景和阴影效果

## 文件结构

```
├── index.html          # 游戏主页面
├── game.js            # 游戏逻辑代码
├── imgs/              # 游戏图片资源文件夹
│   ├── NN.jpg
│   ├── TheLsp.jpg
│   ├── beimiaoguai.jpg
│   ├── chuanxiansheng.jpg
│   ├── guazi.jpg
│   ├── logo.jpg
│   ├── manba.jpg
│   ├── mao.webp
│   ├── momo.jpg
│   ├── shenglong.Wang.jpg
│   ├── tutu.jpg
│   ├── wu.png
│   └── ... (其他图片)
└── README.md          # 说明文档
```

## 如何开始游戏

1. 确保所有文件都在同一目录下
2. 确保imgs文件夹包含游戏所需的图片
3. 用浏览器打开 `index.html` 文件
4. 点击"新游戏"按钮开始游戏

## 技术实现

- **HTML5**：游戏结构和布局
- **CSS3**：样式设计和动画效果
- **JavaScript ES6**：游戏逻辑和交互
- **面向对象编程**：使用类来组织代码结构

## 游戏算法

### 连接算法
游戏实现了三种连接方式：
1. **直线连接**：水平或垂直直线路径
2. **一转角连接**：通过一个转角点连接
3. **二转角连接**：通过游戏边界进行连接

### 卡片生成
- 自动根据游戏板大小生成配对卡片
- 使用Fisher-Yates算法随机打乱卡片位置
- 确保每种图片都有偶数张以形成配对

## 浏览器兼容性

支持所有现代浏览器：
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 游戏截图

游戏界面包含：
- 顶部标题和游戏信息（时间、分数、剩余）
- 中央8x6的游戏网格
- 底部控制按钮
- 美观的渐变背景和卡片阴影效果

享受游戏吧！🎮
