<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>移动端适配测试 - BU连连看</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #2c1810 0%, #4a2c1a 30%, #6b3e2a 60%, #8b5a3c 100%);
            color: white;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }
        
        .device-info {
            background: rgba(79, 195, 247, 0.2);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(6, 52px);
            gap: 2px;
            justify-content: center;
            margin: 15px 0;
        }
        
        .test-card {
            width: 52px;
            height: 52px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #333;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .test-card:active {
            transform: scale(0.95);
            background: rgba(255, 255, 255, 0.7);
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
            margin: 15px 0;
        }
        
        .test-btn {
            padding: 14px 28px;
            background: rgba(79, 195, 247, 0.8);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            min-height: 48px;
            min-width: 120px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .test-btn:active {
            transform: scale(0.95);
            opacity: 0.8;
        }
        
        @media screen and (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .test-grid {
                grid-template-columns: repeat(6, 48px);
            }
            
            .test-card {
                width: 48px;
                height: 48px;
                font-size: 10px;
            }
            
            .test-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
        
        @media screen and (max-width: 320px) {
            .test-grid {
                grid-template-columns: repeat(5, 48px);
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎮 BU连连看 - 移动端适配测试</h1>
        
        <div class="device-info">
            <strong>设备信息:</strong><br>
            屏幕宽度: <span id="screenWidth"></span>px<br>
            屏幕高度: <span id="screenHeight"></span>px<br>
            视口宽度: <span id="viewportWidth"></span>px<br>
            视口高度: <span id="viewportHeight"></span>px<br>
            设备像素比: <span id="devicePixelRatio"></span><br>
            用户代理: <span id="userAgent"></span>
        </div>
        
        <div class="test-section">
            <h3>📱 触摸目标测试</h3>
            <p>以下卡片应该有足够的触摸区域（至少44px x 44px）：</p>
            <div class="test-grid">
                <div class="test-card">1</div>
                <div class="test-card">2</div>
                <div class="test-card">3</div>
                <div class="test-card">4</div>
                <div class="test-card">5</div>
                <div class="test-card">6</div>
                <div class="test-card">7</div>
                <div class="test-card">8</div>
                <div class="test-card">9</div>
                <div class="test-card">10</div>
                <div class="test-card">11</div>
                <div class="test-card">12</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔘 按钮测试</h3>
            <p>按钮应该有足够的触摸区域和良好的反馈：</p>
            <div class="test-buttons">
                <button class="test-btn">🎲 新游戏</button>
                <button class="test-btn">🔄 重新排列</button>
                <button class="test-btn">🔄 重置级别</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📏 响应式测试</h3>
            <p>在不同屏幕尺寸下，布局应该自动调整：</p>
            <ul>
                <li>桌面端 (>1024px): 8列网格</li>
                <li>平板端 (768px-1024px): 8列网格，较小卡片</li>
                <li>大手机 (480px-768px): 8列网格，更小卡片</li>
                <li>小手机 (≤480px): 6列网格，竖屏布局</li>
                <li>超小屏 (≤320px): 6列网格，最小卡片</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🎯 交互测试</h3>
            <p>移动端交互优化：</p>
            <ul>
                <li>✅ 禁用双击缩放</li>
                <li>✅ 移除默认点击高亮</li>
                <li>✅ 触摸反馈动画</li>
                <li>✅ 防止文本选择</li>
                <li>✅ 优化滚动行为</li>
            </ul>
        </div>
        
        <div class="test-section">
            <a href="index.html" style="color: #4fc3f7; text-decoration: none; font-size: 18px;">
                🎮 返回游戏
            </a>
        </div>
    </div>

    <script>
        // 显示设备信息
        function updateDeviceInfo() {
            document.getElementById('screenWidth').textContent = screen.width;
            document.getElementById('screenHeight').textContent = screen.height;
            document.getElementById('viewportWidth').textContent = window.innerWidth;
            document.getElementById('viewportHeight').textContent = window.innerHeight;
            document.getElementById('devicePixelRatio').textContent = window.devicePixelRatio;
            document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 100) + '...';
        }
        
        // 初始化和窗口大小变化时更新
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateDeviceInfo, 500);
        });
        
        // 测试触摸反馈
        document.querySelectorAll('.test-card, .test-btn').forEach(element => {
            element.addEventListener('touchstart', function() {
                this.style.backgroundColor = 'rgba(79, 195, 247, 0.3)';
            });
            
            element.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.backgroundColor = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
