<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>布局修复测试 - BU连连看</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #2c1810 0%, #4a2c1a 30%, #6b3e2a 60%, #8b5a3c 100%);
            color: white;
            min-height: 100vh;
            box-sizing: border-box;
        }
        
        *, *::before, *::after {
            box-sizing: border-box;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .info-panel {
            background: rgba(79, 195, 247, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        /* 复制游戏的实际CSS */
        .test-game-board {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(6, 1fr);
            gap: 5px;
            background-color: rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            width: 100%;
            max-width: 800px;
            height: calc((100vw - 40px) * 6 / 8);
            max-height: calc(800px * 6 / 8);
            max-height: min(calc(800px * 6 / 8), calc(100vh - 200px));
            margin: 0 auto;
            contain: layout style;
        }
        
        .test-card {
            width: 100%;
            height: 100%;
            min-width: 60px;
            min-height: 60px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.3);
            aspect-ratio: 1;
            font-size: 12px;
            font-weight: bold;
            color: #333;
        }
        
        .test-card:hover {
            transform: scale(1.05);
            background: rgba(79, 195, 247, 0.8);
            color: white;
        }
        
        /* 平板设备 */
        @media screen and (max-width: 1024px) {
            body { padding: 15px; }
            .test-game-board {
                max-width: 700px;
                gap: 4px;
                height: calc((100vw - 30px) * 6 / 8);
                max-height: calc(700px * 6 / 8);
                max-height: min(calc(700px * 6 / 8), calc(100vh - 180px));
            }
            .test-card { min-width: 50px; min-height: 50px; }
        }
        
        /* 小平板和大手机 */
        @media screen and (max-width: 768px) {
            body { padding: 10px; }
            .test-game-board {
                max-width: 600px;
                gap: 3px;
                height: calc((100vw - 20px) * 6 / 8);
                max-height: calc(600px * 6 / 8);
                max-height: min(calc(600px * 6 / 8), calc(100vh - 160px));
            }
            .test-card { min-width: 45px; min-height: 45px; }
        }
        
        /* 手机设备 */
        @media screen and (max-width: 480px) {
            body { padding: 8px; }
            .test-game-board {
                grid-template-columns: repeat(6, 1fr);
                grid-template-rows: repeat(8, 1fr);
                gap: 2px;
                width: calc(100vw - 20px);
                max-width: 100%;
                height: calc((100vw - 20px) * 8 / 6);
                max-height: calc(100vh - 140px);
            }
            .test-card { min-width: 48px; min-height: 48px; }
        }
        
        /* 超小屏幕 */
        @media screen and (max-width: 320px) {
            .test-game-board {
                gap: 1px;
                width: calc(100vw - 16px);
                height: calc((100vw - 16px) * 8 / 6);
                max-height: calc(100vh - 120px);
            }
            .test-card { min-width: 44px; min-height: 44px; }
        }
        
        .size-display {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .test-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
        }
        
        .success { background: rgba(76, 175, 80, 0.3); }
        .warning { background: rgba(255, 193, 7, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
    </style>
</head>
<body>
    <div class="size-display">
        视口: <span id="viewport"></span><br>
        游戏板: <span id="boardSize"></span><br>
        设备: <span id="deviceType"></span>
    </div>
    
    <div class="test-container">
        <h1>🔧 布局修复测试</h1>
        
        <div class="info-panel">
            <strong>测试目标：</strong><br>
            ✅ 游戏方块完全铺满游戏区域<br>
            ✅ 纵向方向不出现半截方块<br>
            ✅ 保持方块正方形比例<br>
            ✅ 维持触摸友好的最小尺寸<br>
            ✅ 适配所有屏幕尺寸
        </div>
        
        <div class="test-section">
            <h2>🎮 实际游戏板测试</h2>
            <p>以下是使用实际游戏CSS的测试板，应该完全铺满容器：</p>
            
            <div class="test-game-board" id="testBoard">
                <!-- 桌面端：8x6 = 48个方块 -->
                <!-- 手机端：6x8 = 48个方块 -->
            </div>
            
            <div class="test-result" id="testResult">
                正在检测布局...
            </div>
        </div>
        
        <div class="test-section">
            <h3>📏 布局检查点</h3>
            <ul>
                <li><strong>游戏板尺寸：</strong> <span id="boardDimensions">检测中...</span></li>
                <li><strong>方块数量：</strong> <span id="cardCount">检测中...</span></li>
                <li><strong>方块尺寸：</strong> <span id="cardSize">检测中...</span></li>
                <li><strong>间距设置：</strong> <span id="gapSize">检测中...</span></li>
                <li><strong>溢出检查：</strong> <span id="overflowCheck">检测中...</span></li>
            </ul>
        </div>
        
        <div class="test-section">
            <a href="index.html" style="color: #4fc3f7; text-decoration: none; font-size: 18px; margin-right: 20px;">
                🎮 返回游戏
            </a>
            <a href="responsive-demo.html" style="color: #4fc3f7; text-decoration: none; font-size: 18px;">
                📱 查看演示
            </a>
        </div>
    </div>

    <script>
        function createTestCards() {
            const board = document.getElementById('testBoard');
            const isMobile = window.innerWidth <= 480;
            const cardCount = 48; // 8x6 或 6x8 都是48个
            
            board.innerHTML = '';
            for (let i = 1; i <= cardCount; i++) {
                const card = document.createElement('div');
                card.className = 'test-card';
                card.textContent = i;
                board.appendChild(card);
            }
        }
        
        function updateSizeInfo() {
            const viewport = `${window.innerWidth}×${window.innerHeight}`;
            const board = document.getElementById('testBoard');
            const boardRect = board.getBoundingClientRect();
            const boardSize = `${Math.round(boardRect.width)}×${Math.round(boardRect.height)}`;
            
            let deviceType = 'Desktop';
            if (window.innerWidth <= 320) deviceType = 'Ultra Small';
            else if (window.innerWidth <= 480) deviceType = 'Mobile';
            else if (window.innerWidth <= 768) deviceType = 'Large Mobile';
            else if (window.innerWidth <= 1024) deviceType = 'Tablet';
            
            document.getElementById('viewport').textContent = viewport;
            document.getElementById('boardSize').textContent = boardSize;
            document.getElementById('deviceType').textContent = deviceType;
        }
        
        function runLayoutTest() {
            const board = document.getElementById('testBoard');
            const cards = board.querySelectorAll('.test-card');
            const boardRect = board.getBoundingClientRect();
            const firstCard = cards[0];
            const lastCard = cards[cards.length - 1];
            
            if (!firstCard || !lastCard) return;
            
            const firstCardRect = firstCard.getBoundingClientRect();
            const lastCardRect = lastCard.getBoundingClientRect();
            
            // 检查布局
            const boardDimensions = `${Math.round(boardRect.width)}×${Math.round(boardRect.height)}px`;
            const cardCount = cards.length;
            const cardSize = `${Math.round(firstCardRect.width)}×${Math.round(firstCardRect.height)}px`;
            const computedStyle = window.getComputedStyle(board);
            const gapSize = computedStyle.gap;
            
            // 检查是否有溢出
            const isOverflowing = boardRect.height > window.innerHeight - 100;
            const isComplete = lastCardRect.bottom <= boardRect.bottom + 5; // 5px tolerance
            
            document.getElementById('boardDimensions').textContent = boardDimensions;
            document.getElementById('cardCount').textContent = cardCount;
            document.getElementById('cardSize').textContent = cardSize;
            document.getElementById('gapSize').textContent = gapSize;
            document.getElementById('overflowCheck').textContent = isOverflowing ? '❌ 溢出' : '✅ 正常';
            
            // 显示测试结果
            const resultDiv = document.getElementById('testResult');
            if (isComplete && !isOverflowing) {
                resultDiv.className = 'test-result success';
                resultDiv.textContent = '✅ 布局测试通过！游戏板完全铺满，无溢出问题。';
            } else if (isComplete && isOverflowing) {
                resultDiv.className = 'test-result warning';
                resultDiv.textContent = '⚠️ 布局基本正常，但可能在某些设备上会溢出视口。';
            } else {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '❌ 布局存在问题，方块未完全铺满游戏区域。';
            }
        }
        
        function init() {
            createTestCards();
            updateSizeInfo();
            setTimeout(runLayoutTest, 100);
        }
        
        init();
        window.addEventListener('resize', () => {
            createTestCards();
            updateSizeInfo();
            setTimeout(runLayoutTest, 100);
        });
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                createTestCards();
                updateSizeInfo();
                setTimeout(runLayoutTest, 100);
            }, 500);
        });
    </script>
</body>
</html>
