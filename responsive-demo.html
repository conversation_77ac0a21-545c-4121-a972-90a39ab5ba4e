<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>响应式铺满演示 - BU连连看</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #2c1810 0%, #4a2c1a 30%, #6b3e2a 60%, #8b5a3c 100%);
            color: white;
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .size-info {
            background: rgba(79, 195, 247, 0.2);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        /* 桌面端演示 */
        .desktop-demo {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .demo-board {
            display: grid;
            gap: 5px;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 10px;
            margin: 15px 0;
        }
        
        .desktop-demo .demo-board {
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(6, 1fr);
            height: calc(100% * 6 / 8);
            max-height: 400px;
        }

        .tablet-demo .demo-board {
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(6, 1fr);
            max-width: 700px;
            height: calc(100% * 6 / 8);
            max-height: 350px;
        }

        .mobile-demo .demo-board {
            grid-template-columns: repeat(6, 1fr);
            grid-template-rows: repeat(8, 1fr);
            max-width: 400px;
            height: calc(100% * 8 / 6);
            max-height: 500px;
        }
        
        .demo-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #333;
            font-weight: bold;
            aspect-ratio: 1;
            min-width: 30px;
            min-height: 30px;
            transition: all 0.2s ease;
        }
        
        .demo-card:hover {
            background: rgba(79, 195, 247, 0.8);
            color: white;
            transform: scale(1.05);
        }
        
        .current-size {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 1000;
        }
        
        @media screen and (max-width: 1024px) {
            .desktop-demo { display: none; }
        }
        
        @media screen and (max-width: 768px) {
            .tablet-demo { display: none; }
        }
        
        @media screen and (min-width: 769px) {
            .mobile-demo { display: none; }
        }
        
        @media screen and (max-width: 480px) {
            body { padding: 10px; }
            .demo-section { padding: 15px; }
        }
    </style>
</head>
<body>
    <div class="current-size">
        屏幕: <span id="screenSize"></span><br>
        视口: <span id="viewportSize"></span>
    </div>
    
    <div class="demo-container">
        <h1>🎮 响应式铺满演示</h1>
        <p>以下演示展示了游戏方块如何在不同屏幕尺寸下自动铺满游戏区域：</p>
        
        <!-- 桌面端演示 -->
        <div class="demo-section desktop-demo">
            <h2>🖥️ 桌面端布局 (>1024px)</h2>
            <div class="size-info">
                <strong>特点：</strong> 8列×6行，方块铺满整个游戏区域，最大宽度800px
            </div>
            <div class="demo-board">
                <div class="demo-card">1</div><div class="demo-card">2</div><div class="demo-card">3</div><div class="demo-card">4</div>
                <div class="demo-card">5</div><div class="demo-card">6</div><div class="demo-card">7</div><div class="demo-card">8</div>
                <div class="demo-card">9</div><div class="demo-card">10</div><div class="demo-card">11</div><div class="demo-card">12</div>
                <div class="demo-card">13</div><div class="demo-card">14</div><div class="demo-card">15</div><div class="demo-card">16</div>
                <div class="demo-card">17</div><div class="demo-card">18</div><div class="demo-card">19</div><div class="demo-card">20</div>
                <div class="demo-card">21</div><div class="demo-card">22</div><div class="demo-card">23</div><div class="demo-card">24</div>
                <div class="demo-card">25</div><div class="demo-card">26</div><div class="demo-card">27</div><div class="demo-card">28</div>
                <div class="demo-card">29</div><div class="demo-card">30</div><div class="demo-card">31</div><div class="demo-card">32</div>
                <div class="demo-card">33</div><div class="demo-card">34</div><div class="demo-card">35</div><div class="demo-card">36</div>
                <div class="demo-card">37</div><div class="demo-card">38</div><div class="demo-card">39</div><div class="demo-card">40</div>
                <div class="demo-card">41</div><div class="demo-card">42</div><div class="demo-card">43</div><div class="demo-card">44</div>
                <div class="demo-card">45</div><div class="demo-card">46</div><div class="demo-card">47</div><div class="demo-card">48</div>
            </div>
        </div>
        
        <!-- 平板端演示 -->
        <div class="demo-section tablet-demo">
            <h2>📱 平板端布局 (768px-1024px)</h2>
            <div class="size-info">
                <strong>特点：</strong> 8列×6行，自适应屏幕宽度，最大宽度700px
            </div>
            <div class="demo-board">
                <div class="demo-card">1</div><div class="demo-card">2</div><div class="demo-card">3</div><div class="demo-card">4</div>
                <div class="demo-card">5</div><div class="demo-card">6</div><div class="demo-card">7</div><div class="demo-card">8</div>
                <div class="demo-card">9</div><div class="demo-card">10</div><div class="demo-card">11</div><div class="demo-card">12</div>
                <div class="demo-card">13</div><div class="demo-card">14</div><div class="demo-card">15</div><div class="demo-card">16</div>
                <div class="demo-card">17</div><div class="demo-card">18</div><div class="demo-card">19</div><div class="demo-card">20</div>
                <div class="demo-card">21</div><div class="demo-card">22</div><div class="demo-card">23</div><div class="demo-card">24</div>
                <div class="demo-card">25</div><div class="demo-card">26</div><div class="demo-card">27</div><div class="demo-card">28</div>
                <div class="demo-card">29</div><div class="demo-card">30</div><div class="demo-card">31</div><div class="demo-card">32</div>
                <div class="demo-card">33</div><div class="demo-card">34</div><div class="demo-card">35</div><div class="demo-card">36</div>
                <div class="demo-card">37</div><div class="demo-card">38</div><div class="demo-card">39</div><div class="demo-card">40</div>
                <div class="demo-card">41</div><div class="demo-card">42</div><div class="demo-card">43</div><div class="demo-card">44</div>
                <div class="demo-card">45</div><div class="demo-card">46</div><div class="demo-card">47</div><div class="demo-card">48</div>
            </div>
        </div>
        
        <!-- 手机端演示 -->
        <div class="demo-section mobile-demo">
            <h2>📱 手机端布局 (≤768px)</h2>
            <div class="size-info">
                <strong>特点：</strong> 6列×8行，竖屏优化，铺满屏幕宽度
            </div>
            <div class="demo-board">
                <div class="demo-card">1</div><div class="demo-card">2</div><div class="demo-card">3</div>
                <div class="demo-card">4</div><div class="demo-card">5</div><div class="demo-card">6</div>
                <div class="demo-card">7</div><div class="demo-card">8</div><div class="demo-card">9</div>
                <div class="demo-card">10</div><div class="demo-card">11</div><div class="demo-card">12</div>
                <div class="demo-card">13</div><div class="demo-card">14</div><div class="demo-card">15</div>
                <div class="demo-card">16</div><div class="demo-card">17</div><div class="demo-card">18</div>
                <div class="demo-card">19</div><div class="demo-card">20</div><div class="demo-card">21</div>
                <div class="demo-card">22</div><div class="demo-card">23</div><div class="demo-card">24</div>
                <div class="demo-card">25</div><div class="demo-card">26</div><div class="demo-card">27</div>
                <div class="demo-card">28</div><div class="demo-card">29</div><div class="demo-card">30</div>
                <div class="demo-card">31</div><div class="demo-card">32</div><div class="demo-card">33</div>
                <div class="demo-card">34</div><div class="demo-card">35</div><div class="demo-card">36</div>
                <div class="demo-card">37</div><div class="demo-card">38</div><div class="demo-card">39</div>
                <div class="demo-card">40</div><div class="demo-card">41</div><div class="demo-card">42</div>
                <div class="demo-card">43</div><div class="demo-card">44</div><div class="demo-card">45</div>
                <div class="demo-card">46</div><div class="demo-card">47</div><div class="demo-card">48</div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🎯 技术要点</h3>
            <ul>
                <li><strong>CSS Grid + 1fr：</strong> 使用 <code>grid-template-columns: repeat(8, 1fr)</code> 让方块自动填满宽度</li>
                <li><strong>aspect-ratio：</strong> 保持游戏板的宽高比例，确保视觉效果</li>
                <li><strong>响应式断点：</strong> 不同屏幕尺寸使用不同的列数和比例</li>
                <li><strong>最小尺寸：</strong> 确保触摸目标不会太小，保持可用性</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <a href="index.html" style="color: #4fc3f7; text-decoration: none; font-size: 18px; margin-right: 20px;">
                🎮 返回游戏
            </a>
            <a href="mobile-test.html" style="color: #4fc3f7; text-decoration: none; font-size: 18px;">
                🧪 查看测试页面
            </a>
        </div>
    </div>

    <script>
        function updateSizeInfo() {
            document.getElementById('screenSize').textContent = `${screen.width}×${screen.height}`;
            document.getElementById('viewportSize').textContent = `${window.innerWidth}×${window.innerHeight}`;
        }
        
        updateSizeInfo();
        window.addEventListener('resize', updateSizeInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateSizeInfo, 500);
        });
    </script>
</body>
</html>
