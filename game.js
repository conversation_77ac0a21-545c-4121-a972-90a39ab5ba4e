class LinkGame {
    constructor() {
        this.board = [];
        this.selectedCards = [];
        this.matchedPairs = 0;
        this.score = 0;
        this.startTime = null;
        this.timerInterval = null;
        this.gameBoard = document.getElementById('gameBoard');
        this.level = parseInt(localStorage.getItem('linkGameLevel')) || 1;
        this.maxLevel = 5;
        this.currentBackgroundImage = null;

        // 各级别的背景图片列表和对应的页面配色方案
        this.backgroundImages = {
            1: [
                'imgs/back/size1/0087SeeHgy1i3rg9ca4nvj323w35s1l4.jpg',
                'imgs/back/size1/1.jpg',
                'imgs/back/size1/1.webp',
                'imgs/back/size1/2.jpg',
                'imgs/back/size1/3.jpg',
                'imgs/back/size1/4.jpg'
            ],
            2: [
                'imgs/back/size2/1.jpg',
                'imgs/back/size2/2.jpg',
                'imgs/back/size2/3.jpg'
            ],
            3: [
                'imgs/back/size3/1.webp',
                'imgs/back/size3/2.jpg',
                'imgs/back/size3/3.jpg'
            ],
            4: [
                'imgs/back/size4/1.webp',
                'imgs/back/size4/2.webp'
            ],
            5: [
                'imgs/back/size5/1.jpg',
                'imgs/back/size5/2.jpg',
                'imgs/back/size5/3.jpg'
            ]
        };


        
        // 游戏图片列表 - 包含所有可用图片
        this.allImages = [
            'imgs/NN.jpg',
            'imgs/TheLsp.jpg',
            'imgs/aoye.jpg',
            'imgs/beimiaoguai.jpg',
            'imgs/chuanxiansheng.jpg',
            'imgs/guazi.jpg',
            'imgs/huashui.jpg',
            'imgs/logo.jpg',
            'imgs/manba.jpg',
            'imgs/mao.webp',
            'imgs/momo.jpg',
            'imgs/refresh.jpg',
            'imgs/sakura.jpg',
            'imgs/shatang.jpg',
            'imgs/shenglong.Wang.jpg',
            'imgs/shenglong2.jpg',
            'imgs/success.jpg',
            'imgs/tutu.jpg',
            'imgs/tutu2.jpg',
            'imgs/wu.png',
            'imgs/yincha.jpg',
            'imgs/yinshi.jpg',
            'imgs/yuri.jpg',
            'imgs/zgldh.jpg',
            'imgs/zhinai.jpg',
            'imgs/zhu.jpg',
            'imgs/正经人.jpg',
            'imgs/饮精.jpg'
        ];
        
        // 根据级别设置游戏板大小
        this.setGameSize();
        this.totalPairs = (this.rows * this.cols) / 2;

        // 添加窗口大小变化监听器
        this.setupResizeListener();

        this.initGame();
    }
    
    setGameSize() {
        // 检测屏幕尺寸并调整布局
        const isMobile = window.innerWidth <= 480;
        const isTablet = window.innerWidth <= 768 && window.innerWidth > 480;

        // 根据级别和设备类型设置游戏板大小和难度
        if (isMobile) {
            // 手机端：使用6列布局，增加行数
            switch (this.level) {
                case 1:
                    this.rows = 4;
                    this.cols = 6;
                    break;
                case 2:
                    this.rows = 5;
                    this.cols = 6;
                    break;
                case 3:
                    this.rows = 8;
                    this.cols = 6;
                    break;
                case 4:
                    this.rows = 9;
                    this.cols = 6;
                    break;
                case 5:
                    this.rows = 10;
                    this.cols = 6;
                    break;
                default:
                    this.rows = 8;
                    this.cols = 6;
            }
        } else if (isTablet) {
            // 平板端：适中的布局
            switch (this.level) {
                case 1:
                    this.rows = 4;
                    this.cols = 6;
                    break;
                case 2:
                    this.rows = 5;
                    this.cols = 6;
                    break;
                case 3:
                    this.rows = 6;
                    this.cols = 8;
                    break;
                case 4:
                    this.rows = 7;
                    this.cols = 8;
                    break;
                case 5:
                    this.rows = 8;
                    this.cols = 8;
                    break;
                default:
                    this.rows = 6;
                    this.cols = 8;
            }
        } else {
            // 桌面端：原始布局
            switch (this.level) {
                case 1:
                    this.rows = 4;
                    this.cols = 6;
                    break;
                case 2:
                    this.rows = 5;
                    this.cols = 6;
                    break;
                case 3:
                    this.rows = 6;
                    this.cols = 8;
                    break;
                case 4:
                    this.rows = 7;
                    this.cols = 8;
                    break;
                case 5:
                    this.rows = 8;
                    this.cols = 10;
                    break;
                default:
                    this.rows = 6;
                    this.cols = 8;
            }
        }

        // 确保总卡片数为偶数
        const totalCards = this.rows * this.cols;
        if (totalCards % 2 !== 0) {
            this.rows += 1;
        }

        // 不在这里设置CSS网格布局，让CSS媒体查询处理
        // CSS媒体查询会根据屏幕尺寸自动调整网格大小

        // 设置当前级别的随机背景图
        this.setRandomBackground();
    }

    setupResizeListener() {
        let resizeTimeout;
        window.addEventListener('resize', () => {
            // 防抖处理，避免频繁触发
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                const oldRows = this.rows;
                const oldCols = this.cols;

                // 重新计算游戏板大小
                this.setGameSize();

                // 如果布局发生变化，重新开始游戏
                if (oldRows !== this.rows || oldCols !== this.cols) {
                    this.totalPairs = (this.rows * this.cols) / 2;
                    this.startNewGame();
                }
            }, 300);
        });

        // 监听设备方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                const oldRows = this.rows;
                const oldCols = this.cols;

                this.setGameSize();

                if (oldRows !== this.rows || oldCols !== this.cols) {
                    this.totalPairs = (this.rows * this.cols) / 2;
                    this.startNewGame();
                }
            }, 500); // 给设备时间完成方向变化
        });
    }

    setRandomBackground() {
        const levelImages = this.backgroundImages[this.level];
        if (levelImages && levelImages.length > 0) {
            // 随机选择一张背景图
            const randomIndex = Math.floor(Math.random() * levelImages.length);
            this.currentBackgroundImage = levelImages[randomIndex];

            // 预加载图片以确保存在
            const img = new Image();
            img.onload = () => {
                // 图片加载成功，更新游戏板和页面背景
                this.gameBoard.style.backgroundImage = `url('${this.currentBackgroundImage}')`;
                this.setPageBackground();
            };
            img.onerror = () => {
                // 图片加载失败，尝试下一张或使用默认背景
                console.warn(`背景图片加载失败: ${this.currentBackgroundImage}`);
                this.tryNextBackground(levelImages, randomIndex);
            };
            img.src = this.currentBackgroundImage;
        } else {
            // 没有找到对应级别的背景图，使用默认背景
            this.gameBoard.style.backgroundImage = 'none';
            this.setPageBackground();
        }
    }

    setPageBackground() {
        if (this.currentBackgroundImage) {
            // 设置页面背景为同一张图片，但添加模糊效果
            document.body.style.backgroundImage = `url('${this.currentBackgroundImage}')`;
            document.body.style.backgroundSize = 'cover';
            document.body.style.backgroundPosition = 'center';
            document.body.style.backgroundRepeat = 'no-repeat';
            document.body.style.backgroundAttachment = 'fixed';

            // 添加模糊和暗化效果的伪元素
            this.addBlurOverlay();
        } else {
            // 使用默认背景
            document.body.style.backgroundImage = 'none';
            document.body.style.background = 'linear-gradient(135deg, #2c1810 0%, #4a2c1a 30%, #6b3e2a 60%, #8b5a3c 100%)';
            this.removeBlurOverlay();
        }

        // 设置游戏板背景色
        this.gameBoard.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
    }

    addBlurOverlay() {
        // 移除已存在的覆盖层
        this.removeBlurOverlay();

        // 创建模糊覆盖层
        const overlay = document.createElement('div');
        overlay.id = 'background-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(60px);
            -webkit-backdrop-filter: blur(8px);
            z-index: -1;
            pointer-events: none;
        `;
        document.body.appendChild(overlay);
    }

    removeBlurOverlay() {
        const existingOverlay = document.getElementById('background-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }
    }

    tryNextBackground(levelImages, failedIndex) {
        // 尝试使用其他背景图
        const availableImages = levelImages.filter((_, index) => index !== failedIndex);
        if (availableImages.length > 0) {
            const randomIndex = Math.floor(Math.random() * availableImages.length);
            this.currentBackgroundImage = availableImages[randomIndex];

            const img = new Image();
            img.onload = () => {
                this.gameBoard.style.backgroundImage = `url('${this.currentBackgroundImage}')`;
                this.setPageBackground();
            };
            img.onerror = () => {
                // 所有图片都失败，使用默认背景
                this.gameBoard.style.backgroundImage = 'none';
                this.currentBackgroundImage = null;
                this.setPageBackground();
            };
            img.src = this.currentBackgroundImage;
        } else {
            // 使用默认背景
            this.gameBoard.style.backgroundImage = 'none';
            this.currentBackgroundImage = null;
            this.setPageBackground();
        }
    }

    initGame() {
        this.createBoard();
        this.startTimer();
        this.updateUI();
    }

    selectRandomImages(count) {
        // 如果需要的图片数量大于可用图片数量，则重复使用图片
        if (count <= this.allImages.length) {
            // 随机选择不重复的图片
            const shuffled = [...this.allImages];
            this.shuffleArray(shuffled);
            return shuffled.slice(0, count);
        } else {
            // 需要重复使用图片
            const selected = [];
            const shuffled = [...this.allImages];
            this.shuffleArray(shuffled);

            while (selected.length < count) {
                for (let i = 0; i < shuffled.length && selected.length < count; i++) {
                    selected.push(shuffled[i]);
                }
                // 如果还需要更多图片，重新打乱顺序
                if (selected.length < count) {
                    this.shuffleArray(shuffled);
                }
            }
            return selected;
        }
    }
    
    createBoard() {
        // 随机选择本局游戏要使用的图片
        const selectedImages = this.selectRandomImages(this.totalPairs);

        // 创建配对的图片数组
        const gameImages = [];
        for (let i = 0; i < this.totalPairs; i++) {
            gameImages.push(selectedImages[i], selectedImages[i]);
        }
        
        // 打乱数组
        this.shuffleArray(gameImages);
        
        // 创建游戏板
        this.board = [];
        this.gameBoard.innerHTML = '';
        
        for (let i = 0; i < this.rows; i++) {
            this.board[i] = [];
            for (let j = 0; j < this.cols; j++) {
                const index = i * this.cols + j;
                const card = {
                    row: i,
                    col: j,
                    image: gameImages[index],
                    matched: false,
                    element: null
                };
                
                const cardElement = this.createCardElement(card, index);
                card.element = cardElement;
                this.board[i][j] = card;
                this.gameBoard.appendChild(cardElement);
            }
        }
    }
    
    createCardElement(card, index) {
        const cardElement = document.createElement('div');
        cardElement.className = 'card';
        cardElement.dataset.index = index;
        cardElement.dataset.row = card.row;
        cardElement.dataset.col = card.col;
        
        const img = document.createElement('img');
        img.src = card.image;
        img.alt = 'game card';
        cardElement.appendChild(img);
        
        cardElement.addEventListener('click', () => this.handleCardClick(card));
        
        return cardElement;
    }
    
    handleCardClick(card) {
        if (card.matched || this.selectedCards.includes(card) || this.selectedCards.length >= 2) {
            return;
        }
        
        // 选中卡片
        card.element.classList.add('selected');
        this.selectedCards.push(card);
        
        if (this.selectedCards.length === 2) {
            setTimeout(() => this.checkMatch(), 500);
        }
    }
    
    checkMatch() {
        const [card1, card2] = this.selectedCards;

        if (card1.image === card2.image && this.canConnect(card1, card2)) {
            // 匹配成功
            this.handleMatch(card1, card2);
        } else {
            // 匹配失败
            console.log(`连接失败: (${card1.row},${card1.col}) -> (${card2.row},${card2.col})`);
            card1.element.classList.remove('selected');
            card2.element.classList.remove('selected');
        }

        this.selectedCards = [];
    }
    
    canConnect(card1, card2) {
        // 连接算法：检查是否可以通过直线或两条直线连接
        return this.findPath(card1, card2);
    }
    
    findPath(start, end) {
        // 直线连接
        if (this.canDirectConnect(start, end)) {
            return true;
        }
        
        // 一个转角连接
        if (this.canOneCornerConnect(start, end)) {
            return true;
        }
        
        // 两个转角连接
        if (this.canTwoCornerConnect(start, end)) {
            return true;
        }
        
        return false;
    }
    
    canDirectConnect(start, end) {
        // 检查起点和终点是否在游戏板范围内
        if (start.row < 0 || start.row >= this.rows || start.col < 0 || start.col >= this.cols ||
            end.row < 0 || end.row >= this.rows || end.col < 0 || end.col >= this.cols) {
            // 如果有点在边界外，只要在同一行或同一列就可以连接
            return start.row === end.row || start.col === end.col;
        }

        if (start.row === end.row) {
            // 水平连接
            const minCol = Math.min(start.col, end.col);
            const maxCol = Math.max(start.col, end.col);
            for (let col = minCol + 1; col < maxCol; col++) {
                // 确保检查的位置在游戏板范围内
                if (col >= 0 && col < this.cols) {
                    if (!this.board[start.row][col].matched) {
                        return false;
                    }
                }
            }
            return true;
        } else if (start.col === end.col) {
            // 垂直连接
            const minRow = Math.min(start.row, end.row);
            const maxRow = Math.max(start.row, end.row);
            for (let row = minRow + 1; row < maxRow; row++) {
                // 确保检查的位置在游戏板范围内
                if (row >= 0 && row < this.rows) {
                    if (!this.board[row][start.col].matched) {
                        return false;
                    }
                }
            }
            return true;
        }
        return false;
    }
    
    canOneCornerConnect(start, end) {
        // 尝试通过一个转角点连接
        const corner1 = { row: start.row, col: end.col };
        const corner2 = { row: end.row, col: start.col };

        // 检查转角点1：(start.row, end.col) - 避免与起点终点重合
        if (!(corner1.row === start.row && corner1.col === start.col) &&
            !(corner1.row === end.row && corner1.col === end.col)) {
            if (this.isValidCorner(corner1) &&
                this.canDirectConnect(start, corner1) &&
                this.canDirectConnect(corner1, end)) {
                return true;
            }
        }

        // 检查转角点2：(end.row, start.col) - 避免与起点终点重合
        if (!(corner2.row === start.row && corner2.col === start.col) &&
            !(corner2.row === end.row && corner2.col === end.col)) {
            if (this.isValidCorner(corner2) &&
                this.canDirectConnect(start, corner2) &&
                this.canDirectConnect(corner2, end)) {
                return true;
            }
        }

        return false;
    }
    
    canTwoCornerConnect(start, end) {
        // 尝试通过两个转角连接

        // 1. 检查内部两个转角连接
        if (this.canInternalTwoCornerConnect(start, end)) return true;

        // 2. 检查通过边界连接
        // 检查通过上边界
        if (this.canConnectThroughBorder(start, end, 'top')) return true;
        // 检查通过下边界
        if (this.canConnectThroughBorder(start, end, 'bottom')) return true;
        // 检查通过左边界
        if (this.canConnectThroughBorder(start, end, 'left')) return true;
        // 检查通过右边界
        if (this.canConnectThroughBorder(start, end, 'right')) return true;

        return false;
    }

    canInternalTwoCornerConnect(start, end) {
        // 使用可达点集合的算法
        // 1. 找出起点可达的所有点
        // 2. 找出终点可达的所有点
        // 3. 寻找可以连接的中间点

        const startReachable = this.getReachablePoints(start);
        const endReachable = this.getReachablePoints(end);

        // 检查是否有可连接的中间点对
        for (const startPoint of startReachable) {
            for (const endPoint of endReachable) {
                // 如果两个点在同一行或同一列，且中间路径可通过
                if ((startPoint.row === endPoint.row || startPoint.col === endPoint.col) &&
                    this.canDirectConnect(startPoint, endPoint)) {
                    return true;
                }
            }
        }

        return false;
    }

    getReachablePoints(point) {
        const reachable = [];

        // 向上扩展
        for (let row = point.row - 1; row >= -1; row--) {
            const p = { row: row, col: point.col };
            if (row >= 0 && row < this.rows && point.col >= 0 && point.col < this.cols) {
                if (!this.board[row][point.col].matched) {
                    break; // 遇到障碍物停止
                }
            }
            reachable.push(p);
        }

        // 向下扩展
        for (let row = point.row + 1; row <= this.rows; row++) {
            const p = { row: row, col: point.col };
            if (row >= 0 && row < this.rows && point.col >= 0 && point.col < this.cols) {
                if (!this.board[row][point.col].matched) {
                    break; // 遇到障碍物停止
                }
            }
            reachable.push(p);
        }

        // 向左扩展
        for (let col = point.col - 1; col >= -1; col--) {
            const p = { row: point.row, col: col };
            if (col >= 0 && col < this.cols && point.row >= 0 && point.row < this.rows) {
                if (!this.board[point.row][col].matched) {
                    break; // 遇到障碍物停止
                }
            }
            reachable.push(p);
        }

        // 向右扩展
        for (let col = point.col + 1; col <= this.cols; col++) {
            const p = { row: point.row, col: col };
            if (col >= 0 && col < this.cols && point.row >= 0 && point.row < this.rows) {
                if (!this.board[point.row][col].matched) {
                    break; // 遇到障碍物停止
                }
            }
            reachable.push(p);
        }

        return reachable;
    }

    isValidTwoCornerPath(start, corner1, corner2, end) {
        // 检查转角点不能与起点终点重合
        if ((corner1.row === start.row && corner1.col === start.col) ||
            (corner1.row === end.row && corner1.col === end.col) ||
            (corner2.row === start.row && corner2.col === start.col) ||
            (corner2.row === end.row && corner2.col === end.col)) {
            return false;
        }

        // 检查转角点是否可通过
        if (!this.isValidCorner(corner1) || !this.isValidCorner(corner2)) {
            return false;
        }

        // 检查三段路径是否都可通过
        return this.canDirectConnect(start, corner1) &&
               this.canDirectConnect(corner1, corner2) &&
               this.canDirectConnect(corner2, end);
    }



    canConnectThroughBorder(start, end, border) {
        let borderRow, borderCol;

        switch (border) {
            case 'top':
                borderRow = -1;
                // 检查是否可以从start到顶部边界，再到end
                return this.canVerticalToHorizontal(start, end, borderRow);
            case 'bottom':
                borderRow = this.rows;
                return this.canVerticalToHorizontal(start, end, borderRow);
            case 'left':
                borderCol = -1;
                return this.canHorizontalToVertical(start, end, borderCol);
            case 'right':
                borderCol = this.cols;
                return this.canHorizontalToVertical(start, end, borderCol);
        }
        return false;
    }

    canVerticalToHorizontal(start, end, borderRow) {
        // 检查从start垂直到边界，水平移动，再垂直到end
        const canStartToBorder = this.canVerticalPath(start.row, borderRow, start.col);
        const canEndToBorder = this.canVerticalPath(end.row, borderRow, end.col);
        return canStartToBorder && canEndToBorder;
    }

    canHorizontalToVertical(start, end, borderCol) {
        // 检查从start水平到边界，垂直移动，再水平到end
        const canStartToBorder = this.canHorizontalPath(start.col, borderCol, start.row);
        const canEndToBorder = this.canHorizontalPath(end.col, borderCol, end.row);
        return canStartToBorder && canEndToBorder;
    }

    canVerticalPath(fromRow, toRow, col) {
        if (col < 0 || col >= this.cols) return true;
        const minRow = Math.min(fromRow, toRow);
        const maxRow = Math.max(fromRow, toRow);

        for (let row = Math.max(0, minRow); row <= Math.min(this.rows - 1, maxRow); row++) {
            if (row !== fromRow && !this.board[row][col].matched) {
                return false;
            }
        }
        return true;
    }

    canHorizontalPath(fromCol, toCol, row) {
        if (row < 0 || row >= this.rows) return true;
        const minCol = Math.min(fromCol, toCol);
        const maxCol = Math.max(fromCol, toCol);

        for (let col = Math.max(0, minCol); col <= Math.min(this.cols - 1, maxCol); col++) {
            if (col !== fromCol && !this.board[row][col].matched) {
                return false;
            }
        }
        return true;
    }
    
    isValidCorner(corner) {
        if (corner.row < 0 || corner.row >= this.rows || 
            corner.col < 0 || corner.col >= this.cols) {
            return true; // 边界外视为可通过
        }
        return this.board[corner.row][corner.col].matched;
    }
    
    handleMatch(card1, card2) {
        card1.matched = true;
        card2.matched = true;

        // 添加匹配成功动画
        card1.element.classList.add('match-success');
        card2.element.classList.add('match-success');
        card1.element.classList.remove('selected');
        card2.element.classList.remove('selected');

        // 创建粒子效果
        this.createParticleEffect(card1.element);
        this.createParticleEffect(card2.element);

        // 延迟添加matched类，让卡片完全消失，露出背景图
        setTimeout(() => {
            card1.element.classList.remove('match-success');
            card2.element.classList.remove('match-success');
            card1.element.classList.add('matched');
            card2.element.classList.add('matched');
        }, 800);

        this.matchedPairs++;
        this.score += 100;
        this.updateUI();

        if (this.matchedPairs === this.totalPairs) {
            setTimeout(() => this.gameWin(), 1200);
        }
    }
    

    
    createParticleEffect(cardElement) {
        const rect = cardElement.getBoundingClientRect();
        const boardRect = this.gameBoard.getBoundingClientRect();

        // 计算卡片相对于游戏板的位置
        const cardCenterX = rect.left + rect.width / 2 - boardRect.left;
        const cardCenterY = rect.top + rect.height / 2 - boardRect.top;

        // 创建多个粒子
        const particleCount = 12;
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = `particle particle-${(i % 5) + 1}`;

            // 随机方向和距离
            const angle = (i / particleCount) * Math.PI * 2 + (Math.random() - 0.5) * 0.5;
            const distance = 50 + Math.random() * 30;
            const dx = Math.cos(angle) * distance;
            const dy = Math.sin(angle) * distance;

            // 设置粒子初始位置
            particle.style.left = cardCenterX + 'px';
            particle.style.top = cardCenterY + 'px';
            particle.style.setProperty('--dx', dx + 'px');
            particle.style.setProperty('--dy', dy + 'px');

            // 添加到游戏板
            this.gameBoard.appendChild(particle);

            // 启动动画
            setTimeout(() => {
                particle.classList.add('particle-animate');
            }, 10);

            // 动画结束后移除粒子
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 800);
        }
    }

    shuffleRemainingCards() {
        // 收集所有未匹配的卡片信息
        const remainingCards = [];
        for (let i = 0; i < this.rows; i++) {
            for (let j = 0; j < this.cols; j++) {
                if (!this.board[i][j].matched) {
                    remainingCards.push({
                        image: this.board[i][j].image,
                        position: { row: i, col: j }
                    });
                }
            }
        }

        // 提取图片数组并打乱
        const images = remainingCards.map(card => card.image);
        this.shuffleArray(images);

        // 将打乱后的图片重新分配给未匹配的卡片
        let imageIndex = 0;
        for (let i = 0; i < this.rows; i++) {
            for (let j = 0; j < this.cols; j++) {
                if (!this.board[i][j].matched) {
                    this.board[i][j].image = images[imageIndex];
                    // 更新卡片元素的图片
                    const img = this.board[i][j].element.querySelector('img');
                    if (img) {
                        img.src = images[imageIndex];
                    }
                    imageIndex++;
                }
            }
        }

        // 清除当前选中状态
        this.selectedCards.forEach(card => {
            card.element.classList.remove('selected');
        });
        this.selectedCards = [];
    }

    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }
    
    startTimer() {
        this.startTime = Date.now();
        this.timerInterval = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
            const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
            const seconds = (elapsed % 60).toString().padStart(2, '0');
            document.getElementById('timer').textContent = `${minutes}:${seconds}`;
        }, 1000);
    }
    
    updateUI() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('remaining').textContent = this.totalPairs - this.matchedPairs;
        document.getElementById('level').textContent = this.level;
    }
    
    gameWin() {
        clearInterval(this.timerInterval);

        // 级别提升逻辑
        if (this.level < this.maxLevel) {
            this.level++;
            localStorage.setItem('linkGameLevel', this.level.toString());
            this.showMessage(`🎉 成功！升级到第${this.level}级！请点击新游戏开始下一级吧🎉`);
        } else {
            this.showMessage('🎉 恭喜通关所有级别！🎉');
        }

        this.updateUI();
    }
    
    showMessage(text) {
        const message = document.getElementById('message');
        message.textContent = text;
        message.style.display = 'block';
        setTimeout(() => {
            message.style.display = 'none';
        }, 3000);
    }
}

// 全局游戏实例
let game;

// 游戏控制函数
function startNewGame() {
    if (game && game.timerInterval) {
        clearInterval(game.timerInterval);
    }
    game = new LinkGame();
}

function shuffleCards() {
    if (game) {
        game.shuffleRemainingCards();
    }
}

function resetLevel() {
    if (game) {
        game.level = 1;
        localStorage.setItem('linkGameLevel', '1');
        startNewGame();
    }
}

function debugWin() {
    if (game) {
        // 立即匹配所有剩余的卡片
        for (let i = 0; i < game.rows; i++) {
            for (let j = 0; j < game.cols; j++) {
                if (!game.board[i][j].matched) {
                    game.board[i][j].matched = true;
                    game.board[i][j].element.classList.add('matched');
                }
            }
        }

        // 设置匹配对数为总数
        game.matchedPairs = game.totalPairs;

        // 更新UI
        game.updateUI();

        // 触发游戏胜利
        setTimeout(() => {
            game.gameWin();
        }, 500);
    }
}

// 页面加载完成后启动游戏
document.addEventListener('DOMContentLoaded', () => {
    startNewGame();
});
