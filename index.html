<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BU连连看</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c1810 0%, #4a2c1a 30%, #6b3e2a 60%, #8b5a3c 100%);
            min-height: 90vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: all 0.8s ease;
            position: relative;
        }

        .game-header {
            text-align: center;
            margin-bottom: 20px;
            color: white;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 15px 30px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .game-info {
            display: flex;
            gap: 30px;
            margin-bottom: 20px;
            color: white;
            font-size: 18px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 15px 25px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .game-board {
            display: grid;
            grid-template-columns: repeat(8, 80px);
            grid-template-rows: repeat(6, 80px);
            gap: 5px;
            background-color: rgba(255, 255, 255, 0.1);
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            /* padding: 20px; */
            /* border-radius: 20px; */
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            position: relative;
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .card {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            /* border-radius: 12px; */
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .card:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 20px rgba(44, 24, 16, 0.5);
        }

        .card.selected {
            border: 3px solid #4fc3f7;
            transform: scale(1.1);
            box-shadow: 0 0 25px rgba(79, 195, 247, 0.8);
            background: rgba(255, 255, 255, 0.95);
        }

        .card.matched {
            opacity: 0.3;
            pointer-events: none;
            transform: scale(0.9);
        }

        .card img {
            width: 70px;
            height: 70px;
            object-fit: cover;
            border-radius: 5px;
        }

        .controls {
            margin-top: 20px;
            display: flex;
            gap: 15px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 15px 25px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .btn {
            padding: 12px 24px;
            font-size: 16px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .btn-primary {
            background: rgba(79, 195, 247, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-primary:hover {
            background: rgba(79, 195, 247, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
        }

        .btn-secondary {
            background: rgba(156, 39, 176, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(156, 39, 176, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
        }

        /* 隐藏的调试按钮 */
        .debug-hidden-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: transparent;
            border: none;
            cursor: pointer;
            opacity: 0;
            z-index: 9999;
        }

        .debug-hidden-btn:hover {
            opacity: 0.1;
            background: rgba(231, 76, 60, 0.1);
            border-radius: 50%;
        }

        .message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(145deg, rgba(212, 165, 116, 0.95), rgba(166, 124, 82, 0.95));
            color: white;
            padding: 25px 50px;
            border-radius: 20px;
            font-size: 28px;
            font-weight: bold;
            z-index: 1000;
            display: none;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 215, 180, 0.4);
            box-shadow: 0 15px 35px rgba(44, 24, 16, 0.5);
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
        }

        /* 粒子动画样式 */
        .particle {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #d4a574;
            border-radius: 50%;
            pointer-events: none;
            z-index: 100;
        }

        .particle-1 { background: #d4a574; }
        .particle-2 { background: #c19660; }
        .particle-3 { background: #a67c52; }
        .particle-4 { background: #8b5a3c; }
        .particle-5 { background: #6b3e2a; }

        @keyframes particleExplode {
            0% {
                opacity: 1;
                transform: scale(1) translate(0, 0);
            }
            100% {
                opacity: 0;
                transform: scale(0.3) translate(var(--dx), var(--dy));
            }
        }

        .particle-animate {
            animation: particleExplode 0.8s ease-out forwards;
        }

        /* 匹配成功的卡片动画 */
        .card.matched {
            opacity: 0;
            pointer-events: none;
            transform: scale(0);
            transition: all 0.8s ease-out;
        }

        .card.match-success {
            animation: matchSuccess 0.6s ease-out forwards;
        }

        @keyframes matchSuccess {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.8;
                box-shadow: 0 0 25px rgba(212, 165, 116, 0.8);
            }
            100% {
                transform: scale(0);
                opacity: 0;
            }
        }


    </style>
</head>
<body>
    <div class="game-header">
        <h1>🎮 BU连连看 🎮</h1>
    </div>
    
    <div class="game-info">
        <div>⏱️ 时间: <span id="timer">00:00</span></div>
        <div>🎯 分数: <span id="score">0</span></div>
        <div>💎 剩余: <span id="remaining">24</span></div>
        <div>🏆 级别: <span id="level">1</span></div>
    </div>

    <div class="game-board" id="gameBoard"></div>

    <div class="controls">
        <button class="btn btn-primary" onclick="startNewGame()">🎲 新游戏</button>
        <button class="btn btn-secondary" onclick="shuffleCards()">🔄 重新排列</button>
        <button class="btn btn-secondary" onclick="resetLevel()">🔄 重置级别</button>
    </div>

    <div class="message" id="message"></div>

    <!-- 隐藏的调试按钮 -->
    <button class="debug-hidden-btn" onclick="debugWin()"></button>

    <script src="game.js"></script>
</body>
</html>
